-- ===== إنشاء videos bucket في Supabase Storage =====

-- 1. إنشاء videos bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'videos',
    'videos',
    true,
    104857600, -- 100MB limit
    ARRAY['video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/webm']
)
ON CONFLICT (id) DO NOTHING;

-- 2. إنشاء policies للـ videos bucket

-- Policy للقراءة العامة
CREATE POLICY "Public Access" ON storage.objects
FOR SELECT USING (bucket_id = 'videos');

-- Policy للرفع للمستخدمين المصادق عليهم
CREATE POLICY "Authenticated users can upload videos" ON storage.objects
FOR INSERT TO authenticated
WITH CHECK (
    bucket_id = 'videos' 
    AND (storage.foldername(name))[1] = 'uploads'
);

-- Policy للتحديث للمستخدمين المصادق عليهم
CREATE POLICY "Authenticated users can update own videos" ON storage.objects
FOR UPDATE TO authenticated
USING (
    bucket_id = 'videos' 
    AND (storage.foldername(name))[1] = 'uploads'
);

-- Policy للحذف للمستخدمين المصادق عليهم
CREATE POLICY "Authenticated users can delete own videos" ON storage.objects
FOR DELETE TO authenticated
USING (
    bucket_id = 'videos' 
    AND (storage.foldername(name))[1] = 'uploads'
);

-- 3. تحديث policies للـ photos bucket لدعم الفيديوهات كـ fallback

-- Policy للرفع (تحديث لدعم الفيديوهات)
DROP POLICY IF EXISTS "Authenticated users can upload photos" ON storage.objects;
CREATE POLICY "Authenticated users can upload files" ON storage.objects
FOR INSERT TO authenticated
WITH CHECK (
    bucket_id = 'photos' 
    AND (storage.foldername(name))[1] = 'uploads'
);

-- Policy للتحديث (تحديث لدعم الفيديوهات)
DROP POLICY IF EXISTS "Authenticated users can update own photos" ON storage.objects;
CREATE POLICY "Authenticated users can update own files" ON storage.objects
FOR UPDATE TO authenticated
USING (
    bucket_id = 'photos' 
    AND (storage.foldername(name))[1] = 'uploads'
);

-- Policy للحذف (تحديث لدعم الفيديوهات)
DROP POLICY IF EXISTS "Authenticated users can delete own photos" ON storage.objects;
CREATE POLICY "Authenticated users can delete own files" ON storage.objects
FOR DELETE TO authenticated
USING (
    bucket_id = 'photos' 
    AND (storage.foldername(name))[1] = 'uploads'
);

-- 4. إنشاء دالة لفحص حالة buckets
CREATE OR REPLACE FUNCTION check_storage_buckets()
RETURNS TABLE (
    bucket_name TEXT,
    is_public BOOLEAN,
    file_size_limit BIGINT,
    allowed_mime_types TEXT[]
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        b.name::TEXT,
        b.public,
        b.file_size_limit,
        b.allowed_mime_types
    FROM storage.buckets b
    WHERE b.id IN ('photos', 'videos')
    ORDER BY b.name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. إنشاء دالة لفحص policies
CREATE OR REPLACE FUNCTION check_storage_policies()
RETURNS TABLE (
    bucket_name TEXT,
    policy_name TEXT,
    policy_command TEXT,
    policy_role TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        CASE 
            WHEN p.cmd LIKE '%videos%' THEN 'videos'
            WHEN p.cmd LIKE '%photos%' THEN 'photos'
            ELSE 'unknown'
        END::TEXT as bucket_name,
        p.policyname::TEXT,
        p.cmd::TEXT,
        CASE 
            WHEN p.roles = '{authenticated}' THEN 'authenticated'
            WHEN p.roles = '{public}' THEN 'public'
            ELSE array_to_string(p.roles, ',')
        END::TEXT
    FROM pg_policies p
    WHERE p.tablename = 'objects' 
    AND p.schemaname = 'storage'
    AND (p.cmd LIKE '%videos%' OR p.cmd LIKE '%photos%')
    ORDER BY bucket_name, p.policyname;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. فحص النتائج
SELECT 'تم إنشاء videos bucket والـ policies بنجاح!' as result;

-- عرض معلومات الـ buckets
SELECT * FROM check_storage_buckets();

-- عرض معلومات الـ policies
SELECT * FROM check_storage_policies();
