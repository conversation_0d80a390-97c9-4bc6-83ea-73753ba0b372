import 'dart:async';
import 'dart:isolate';
import 'dart:ui';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:geolocator/geolocator.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/logger.dart';

/// خدمة تتبع الموقع في الخلفية للـ Live Tracking
/// تستخدم flutter_background_service فقط
class BackgroundLocationService {
  static final BackgroundLocationService _instance = BackgroundLocationService._internal();
  factory BackgroundLocationService() => _instance;
  BackgroundLocationService._internal();

  static const String _serviceName = 'location_tracking_service';

  final _logger = getLogger();
  bool _isRunning = false;

  /// بدء خدمة الخلفية
  Future<bool> startBackgroundService() async {
    try {
      _logger.i('🚀 بدء خدمة تتبع الموقع في الخلفية...');

      // التحقق من الأذونات
      if (!await _checkPermissions()) {
        _logger.e('❌ الأذونات غير متوفرة');
        return false;
      }

      // إعداد خدمة الخلفية
      await _initializeBackgroundService();

      // بدء الخدمة
      final service = FlutterBackgroundService();
      bool isRunning = await service.isRunning();
      
      if (!isRunning) {
        await service.startService();
        _isRunning = true;
        _logger.i('✅ تم بدء خدمة الخلفية بنجاح');
      } else {
        _logger.i('ℹ️ خدمة الخلفية تعمل بالفعل');
        _isRunning = true;
      }

      return true;
    } catch (e) {
      _logger.e('❌ فشل في بدء خدمة الخلفية: $e');
      return false;
    }
  }

  /// إيقاف خدمة الخلفية
  Future<void> stopBackgroundService() async {
    try {
      final service = FlutterBackgroundService();
      service.invoke('stop_service'); // إزالة await لأن invoke ترجع void
      _isRunning = false;
      _logger.i('🛑 تم إيقاف خدمة الخلفية');
    } catch (e) {
      _logger.e('❌ فشل في إيقاف خدمة الخلفية: $e');
    }
  }

  /// فحص الأذونات المطلوبة
  Future<bool> _checkPermissions() async {
    try {
      // فحص إذن الموقع
      LocationPermission locationPermission = await Geolocator.checkPermission();
      if (locationPermission == LocationPermission.denied) {
        locationPermission = await Geolocator.requestPermission();
      }

      if (locationPermission == LocationPermission.deniedForever ||
          locationPermission == LocationPermission.denied) {
        _logger.w('⚠️ إذن الموقع مرفوض');
        return false;
      }

      // فحص إذن الموقع في الخلفية
      if (locationPermission != LocationPermission.always) {
        _logger.w('⚠️ إذن الموقع في الخلفية غير متوفر');
        // يمكن المتابعة بإذن "أثناء الاستخدام"
      }

      // فحص إذن تحسين البطارية
      bool batteryOptimization = await Permission.ignoreBatteryOptimizations.isGranted;
      if (!batteryOptimization) {
        await Permission.ignoreBatteryOptimizations.request();
      }

      return true;
    } catch (e) {
      _logger.e('❌ خطأ في فحص الأذونات: $e');
      return false;
    }
  }

  /// إعداد خدمة الخلفية
  Future<void> _initializeBackgroundService() async {
    try {
      final service = FlutterBackgroundService();

      await service.configure(
        iosConfiguration: IosConfiguration(
          autoStart: false, // تعطيل البدء التلقائي لتجنب المشاكل
          onForeground: onStart,
          onBackground: onIosBackground,
        ),
        androidConfiguration: AndroidConfiguration(
          autoStart: false, // تعطيل البدء التلقائي لتجنب المشاكل
          onStart: onStart,
          isForegroundMode: true,
          autoStartOnBoot: false, // تعطيل البدء مع النظام
          initialNotificationTitle: 'Moon Memory',
          initialNotificationContent: 'تتبع الموقع نشط',
          foregroundServiceNotificationId: 888,
        ),
      );

      _logger.i('✅ تم إعداد خدمة الخلفية بنجاح');
    } catch (e) {
      _logger.e('❌ فشل في إعداد خدمة الخلفية: $e');
      rethrow;
    }
  }

  /// حالة الخدمة
  bool get isRunning => _isRunning;

  /// فحص إذا كانت الخدمة تعمل
  Future<bool> isServiceRunning() async {
    try {
      final service = FlutterBackgroundService();
      return await service.isRunning();
    } catch (e) {
      return false;
    }
  }
}

/// نقطة دخول خدمة الخلفية
@pragma('vm:entry-point')
void onStart(ServiceInstance service) async {
  DartPluginRegistrant.ensureInitialized();
  
  final logger = getLogger();
  logger.i('🔄 بدء خدمة تتبع الموقع في الخلفية');

  // إعداد timer للتحديث كل 30 ثانية
  Timer.periodic(const Duration(seconds: 30), (timer) async {
    try {
      await _updateLocationInBackground(service, logger);
    } catch (e) {
      logger.e('❌ خطأ في تحديث الموقع: $e');
    }
  });

  // الاستماع لأوامر الإيقاف
  service.on('stop_service').listen((event) {
    logger.i('🛑 تم طلب إيقاف الخدمة');
    service.stopSelf();
  });
}

/// معالج iOS للخلفية
@pragma('vm:entry-point')
Future<bool> onIosBackground(ServiceInstance service) async {
  DartPluginRegistrant.ensureInitialized();
  return true;
}

/// تحديث الموقع في الخلفية
Future<void> _updateLocationInBackground(ServiceInstance service, logger) async {
  try {
    // فحص الشروط المطلوبة
    if (!await _checkBackgroundConditions(logger)) {
      return;
    }

    // الحصول على الموقع الحالي
    Position? position = await _getCurrentLocationBackground(logger);
    if (position == null) return;

    // الحصول على معلومات المستخدم
    final prefs = await SharedPreferences.getInstance();
    final userId = prefs.getString('current_user_id');
    final username = prefs.getString('current_username');
    
    if (userId == null) {
      logger.w('⚠️ لا يوجد مستخدم مسجل دخول');
      return;
    }

    // إرسال البيانات لقاعدة البيانات
    await _sendLocationToDatabase(
      userId: userId,
      username: username ?? 'مجهول',
      position: position,
      logger: logger,
    );

    // تحديث حالة الخدمة
    service.invoke('update', {
      'current_date': DateTime.now().toIso8601String(),
      'latitude': position.latitude,
      'longitude': position.longitude,
      'accuracy': position.accuracy,
    });

    logger.d('📍 تم تحديث الموقع في الخلفية');

  } catch (e) {
    logger.e('❌ خطأ في تحديث الموقع في الخلفية: $e');
  }
}

/// فحص الشروط المطلوبة للتحديث
Future<bool> _checkBackgroundConditions(logger) async {
  try {
    // فحص الاتصال بالإنترنت
    final connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult.contains(ConnectivityResult.none)) {
      logger.d('⚠️ لا يوجد اتصال بالإنترنت');
      return false;
    }

    // فحص إذا كان الموقع مفعل
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      logger.d('⚠️ خدمة الموقع غير مفعلة');
      return false;
    }

    return true;
  } catch (e) {
    logger.e('❌ خطأ في فحص الشروط: $e');
    return false;
  }
}

/// الحصول على الموقع في الخلفية
Future<Position?> _getCurrentLocationBackground(logger) async {
  try {
    return await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.medium, // دقة متوسطة لتوفير البطارية
      timeLimit: const Duration(seconds: 15),
    );
  } catch (e) {
    logger.w('❌ فشل في الحصول على الموقع: $e');
    return null;
  }
}

/// إرسال الموقع لقاعدة البيانات
Future<void> _sendLocationToDatabase({
  required String userId,
  required String username,
  required Position position,
  required logger,
}) async {
  try {
    // إعداد Supabase (يجب أن يكون مُعد مسبقاً)
    final prefs = await SharedPreferences.getInstance();
    final supabaseUrl = prefs.getString('supabase_url');
    final supabaseKey = prefs.getString('supabase_key');
    
    if (supabaseUrl == null || supabaseKey == null) {
      logger.w('⚠️ إعدادات Supabase غير متوفرة');
      return;
    }

    // إنشاء client مؤقت
    final supabase = SupabaseClient(supabaseUrl, supabaseKey);

    // إرسال البيانات
    await supabase.rpc('update_live_location', params: {
      'p_user_id': userId,
      'p_username': username,
      'p_latitude': position.latitude,
      'p_longitude': position.longitude,
      'p_accuracy': position.accuracy,
      'p_is_live': true,
      'p_timestamp': DateTime.now().toIso8601String(),
    });

    logger.d('✅ تم إرسال الموقع لقاعدة البيانات');

  } catch (e) {
    logger.e('❌ فشل في إرسال الموقع: $e');
  }
}
